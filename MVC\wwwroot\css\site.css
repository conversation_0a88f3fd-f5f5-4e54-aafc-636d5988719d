/* Bootstrap 3.3.7 Customizations */
body {
  padding-top: 20px;
  padding-bottom: 60px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333;
  background-color: #fff;
}

/* Footer styles */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  padding-top: 20px;
}

/* Panel customizations */
.panel-primary > .panel-heading {
  background-color: #337ab7;
  border-color: #337ab7;
  color: white;
}

/* Table styles */
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > td {
  padding: 5px;
  vertical-align: middle;
}

.table-bordered {
  border: 1px solid #ddd;
}

/* Form control sizes for Bootstrap 3 */
.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

/* Button spacing */
.btn {
  margin-right: 5px;
}

/* MOSCI specific styles */
.no-print {
  margin-bottom: 20px;
}

#inmateTable th {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
}

#inmateTable input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .text-right {
    text-align: left;
    margin-top: 10px;
  }
}

/* Spinning icon for loading indicators */
.glyphicon-spin {
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
