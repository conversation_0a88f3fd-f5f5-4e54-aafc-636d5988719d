is_global = true
build_property.TargetFramework = net6.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Odrdc.Dots
build_property.RootNamespace = Odrdc.Dots
build_property.ProjectDir = c:\Users\<USER>\Documents\augment-projects\MV\MV\MVC\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 6.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = c:\Users\<USER>\Documents\augment-projects\MV\MV\MVC
build_property._RazorSourceGeneratorDebug = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Areas/Transfers/Views/Mosci/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcVHJhbnNmZXJzXFZpZXdzXE1vc2NpXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Areas/Transfers/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcVHJhbnNmZXJzXFZpZXdzXF9WaWV3SW1wb3J0cy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Areas/Transfers/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcVHJhbnNmZXJzXFZpZXdzXF9WaWV3U3RhcnQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/Home/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxFcnJvci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlcnNcRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlcnNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[c:/Users/<USER>/Documents/augment-projects/MV/MV/MVC/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
